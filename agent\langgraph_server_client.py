"""
LangGraph Server Client Example
This shows how to interact with your sales agent via the LangGraph Server API
"""
import asyncio
import httpx
import json
from typing import Dict, Any, Optional

class LangGraphClient:
    """Client for interacting with LangGraph Server"""
    
    def __init__(self, base_url: str = "http://localhost:8123"):
        self.base_url = base_url
        self.client = httpx.AsyncClient()
    
    async def create_thread(self) -> str:
        """Create a new conversation thread"""
        response = await self.client.post(f"{self.base_url}/threads")
        response.raise_for_status()
        return response.json()["thread_id"]
    
    async def invoke_agent(
        self, 
        thread_id: str, 
        message: str, 
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Invoke the agent with a message"""
        
        # Default configuration
        default_config = {
            "configurable": {
                "model": "google_genai:gemini-2.0-flash",
                "mcp_servers": {
                    "pricing": {
                        "url": "http://mcp-pricing:8000/mcp",
                        "transport": "streamable_http",
                    }
                },
                # Galileo configuration (disabled by default)
                "galileo_enabled": False,
                "galileo_project_name": "sales-agent-client",
            }
        }
        
        # Merge with provided config
        if config:
            if "configurable" in config:
                default_config["configurable"].update(config["configurable"])
            else:
                default_config.update(config)
        
        payload = {
            "input": {"messages": [{"role": "user", "content": message}]},
            "config": default_config
        }
        
        response = await self.client.post(
            f"{self.base_url}/threads/{thread_id}/runs",
            json=payload
        )
        response.raise_for_status()
        return response.json()
    
    async def stream_agent(
        self, 
        thread_id: str, 
        message: str, 
        config: Optional[Dict[str, Any]] = None
    ):
        """Stream responses from the agent"""
        
        # Default configuration
        default_config = {
            "configurable": {
                "model": "google_genai:gemini-2.0-flash",
                "mcp_servers": {
                    "pricing": {
                        "url": "http://mcp-pricing:8000/mcp",
                        "transport": "streamable_http",
                    }
                },
                # Galileo configuration (disabled by default)
                "galileo_enabled": False,
                "galileo_project_name": "sales-agent-client",
            }
        }
        
        # Merge with provided config
        if config:
            if "configurable" in config:
                default_config["configurable"].update(config["configurable"])
            else:
                default_config.update(config)
        
        payload = {
            "input": {"messages": [{"role": "user", "content": message}]},
            "config": default_config,
            "stream_mode": "values"
        }
        
        async with self.client.stream(
            "POST",
            f"{self.base_url}/threads/{thread_id}/runs/stream",
            json=payload
        ) as response:
            response.raise_for_status()
            async for line in response.aiter_lines():
                if line.startswith("data: "):
                    try:
                        data = json.loads(line[6:])  # Remove "data: " prefix
                        yield data
                    except json.JSONDecodeError:
                        continue
    
    async def get_thread_history(self, thread_id: str) -> Dict[str, Any]:
        """Get the conversation history for a thread"""
        response = await self.client.get(f"{self.base_url}/threads/{thread_id}/state")
        response.raise_for_status()
        return response.json()
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()

async def example_single_interaction():
    """Example of a single interaction with the agent"""
    client = LangGraphClient()
    
    try:
        # Create a new thread
        thread_id = await client.create_thread()
        print(f"Created thread: {thread_id}")
        
        # Send a message
        message = "I'm interested in ArchiCAD pricing for a small architecture firm with 5 users"
        print(f"User: {message}")
        
        # Get response
        response = await client.invoke_agent(thread_id, message)
        
        # Extract the assistant's response
        if "messages" in response and response["messages"]:
            assistant_message = response["messages"][-1]["content"]
            print(f"Assistant: {assistant_message}")
        
    finally:
        await client.close()

async def example_streaming_interaction():
    """Example of streaming interaction with the agent"""
    client = LangGraphClient()
    
    try:
        # Create a new thread
        thread_id = await client.create_thread()
        print(f"Created thread: {thread_id}")
        
        # Send a message with streaming
        message = "Can you help me understand the different ArchiCAD subscription options?"
        print(f"User: {message}")
        print("Assistant: ", end="", flush=True)
        
        async for chunk in client.stream_agent(thread_id, message):
            if "messages" in chunk and chunk["messages"]:
                # Print the latest message content
                latest_message = chunk["messages"][-1]
                if latest_message["role"] == "assistant":
                    print(latest_message["content"])
                    break
        
    finally:
        await client.close()

async def example_conversation():
    """Example of a multi-turn conversation"""
    client = LangGraphClient()
    
    try:
        # Create a new thread
        thread_id = await client.create_thread()
        print(f"Created thread: {thread_id}")
        print("Sales Agent Chat - Type 'quit' to exit")
        print("-" * 40)
        
        while True:
            # Get user input
            user_input = input("\nUser: ")
            if user_input.lower() in ["quit", "exit", "q"]:
                print("Goodbye!")
                break
            
            # Send message and get response
            try:
                response = await client.invoke_agent(thread_id, user_input)
                
                if "messages" in response and response["messages"]:
                    assistant_message = response["messages"][-1]["content"]
                    print(f"Assistant: {assistant_message}")
                else:
                    print("Assistant: I'm sorry, I couldn't process your request.")
                    
            except Exception as e:
                print(f"Error: {e}")
        
    finally:
        await client.close()

async def example_with_custom_config():
    """Example using custom configuration"""
    client = LangGraphClient()

    try:
        # Create a new thread
        thread_id = await client.create_thread()

        # Custom configuration - you can modify model or other settings
        custom_config = {
            "configurable": {
                "model": "google_genai:gemini-2.0-flash",  # You can change this
                "mcp_servers": {
                    "pricing": {
                        "url": "http://mcp-pricing:8000/mcp",
                        "transport": "streamable_http",
                    }
                }
            }
        }

        message = "What's the pricing for ArchiCAD in Germany?"
        print(f"User: {message}")

        response = await client.invoke_agent(thread_id, message, config=custom_config)

        if "messages" in response and response["messages"]:
            assistant_message = response["messages"][-1]["content"]
            print(f"Assistant: {assistant_message}")

    finally:
        await client.close()

async def example_with_galileo():
    """Example using Galileo observability"""
    client = LangGraphClient()

    try:
        # Create a new thread
        thread_id = await client.create_thread()
        print(f"Created thread: {thread_id}")

        # Configuration with Galileo enabled
        galileo_config = {
            "configurable": {
                "model": "google_genai:gemini-2.0-flash",
                "mcp_servers": {
                    "pricing": {
                        "url": "http://mcp-pricing:8000/mcp",
                        "transport": "streamable_http",
                    }
                },
                # Enable Galileo observability
                "galileo_enabled": True,
                "galileo_project_name": "sales-agent-server-client",
                # galileo_api_key will be read from GALILEO_API_KEY env var
            }
        }

        print("🔭 Testing with Galileo observability enabled")

        # Check if Galileo API key is set
        import os
        if not os.getenv("GALILEO_API_KEY"):
            print("⚠️  GALILEO_API_KEY not set, Galileo will be disabled")
            galileo_config["configurable"]["galileo_enabled"] = False

        # Test conversation
        messages = [
            "Hello, I need information about ArchiCAD pricing",
            "We're a small architecture firm with 5 users",
            "What would be the best subscription option for us?"
        ]

        for i, message in enumerate(messages, 1):
            print(f"\n[Message {i}] User: {message}")

            response = await client.invoke_agent(thread_id, message, config=galileo_config)

            if "messages" in response and response["messages"]:
                assistant_message = response["messages"][-1]["content"]
                print(f"[Message {i}] Assistant: {assistant_message[:150]}...")
            else:
                print(f"[Message {i}] No response received")

        print("\n✅ Conversation completed with Galileo tracking!")
        if os.getenv("GALILEO_API_KEY"):
            print("🔭 Check your Galileo dashboard to see the tracked interactions")

    except Exception as e:
        print(f"Error: {e}")
    finally:
        await client.close()

if __name__ == "__main__":
    print("Choose an example to run:")
    print("1. Single interaction")
    print("2. Streaming interaction")
    print("3. Interactive conversation")
    print("4. Custom configuration")
    
    choice = input("Enter choice (1-4): ")
    
    if choice == "1":
        asyncio.run(example_single_interaction())
    elif choice == "2":
        asyncio.run(example_streaming_interaction())
    elif choice == "3":
        asyncio.run(example_conversation())
    elif choice == "4":
        asyncio.run(example_with_custom_config())
    else:
        print("Invalid choice")
