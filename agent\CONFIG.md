# Sales Agent Configuration Guide

This document explains how to configure the Sales Agent for different environments and use cases.

## Quick Start

1. **Copy the environment template:**
   ```bash
   cp .env.example .env
   ```

2. **Edit `.env` with your API keys:**
   ```bash
   # Required: Add your Google API key for Gemini
   GOOGLE_API_KEY=your_google_api_key_here
   
   # Optional: Add LangSmith API key for tracing
   LANGSMITH_API_KEY=your_langsmith_api_key_here
   ```

3. **Test your configuration:**
   ```bash
   python test_config.py
   ```

## Configuration Files

### Environment Files

- **`.env.example`** - Template with all available options
- **`.env.development`** - Development-specific settings
- **`.env.docker`** - Docker deployment settings
- **`.env`** - Your actual environment file (create from template)

### Configuration Modules

- **`src/agent/config.py`** - Main configuration classes
- **`src/agent/config_utils.py`** - Configuration utilities and validation
- **`langgraph.json`** - LangGraph deployment configuration

## Configuration Options

### Model Configuration

```env
# Language model to use
AGENT_MODEL=google_genai:gemini-2.0-flash

# Model parameters
AGENT_TEMPERATURE=0.7
AGENT_MAX_TOKENS=4096
```

Supported models:
- `google_genai:gemini-2.0-flash` (requires `GOOGLE_API_KEY`)
- `openai:gpt-4` (requires `OPENAI_API_KEY`)
- `openai:gpt-3.5-turbo` (requires `OPENAI_API_KEY`)

### Agent Behavior

```env
# Maximum steps the agent can take
AGENT_MAX_STEPS=25

# Company name for the sales agent
COMPANY_NAME=Graphisoft

# Custom system prompt (optional)
AGENT_SYSTEM_PROMPT=You are a helpful sales assistant...
```

### MCP Server Configuration

```env
# Pricing service (required)
MCP_PRICING_URL=http://mcp-pricing:8000/mcp

# Search service (optional)
MCP_SEARCH_URL=http://mcp-search:8000/mcp
MCP_SEARCH_ENABLED=false
```

### Monitoring and Tracing

#### LangSmith (Recommended)

```env
LANGSMITH_TRACING=true
LANGSMITH_PROJECT=sales-agent
LANGSMITH_API_KEY=your_langsmith_api_key_here
```

#### Galileo (Optional)

```env
GALILEO_ENABLED=false
GALILEO_PROJECT_NAME=sales-agent
GALILEO_API_KEY=your_galileo_api_key_here
```

### Database Configuration

```env
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=sales_agent
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
```

### Development Settings

```env
DEBUG=false
LOG_LEVEL=INFO
```

## Environment-Specific Setup

### Local Development

1. Copy the development template:
   ```bash
   cp .env.development .env
   ```

2. Update with your API keys and local service URLs

3. Start local services (if needed):
   ```bash
   # Start local MCP pricing service
   cd ../pricing
   docker-compose up -d
   ```

### Docker Deployment

1. Use the Docker environment file:
   ```bash
   cp .env.docker .env
   ```

2. Update with production API keys

3. Deploy with Docker Compose:
   ```bash
   docker-compose up -d
   ```

### LangGraph Server

The agent is configured to work with LangGraph Server. The `langgraph.json` file contains:

- Graph definition pointing to `make_graph()` function
- Configuration schema for runtime parameters
- Environment file reference

## Configuration Validation

Use the test script to validate your configuration:

```bash
python test_config.py
```

This will:
- ✅ Check required API keys are present
- ✅ Validate MCP server configurations
- ✅ Test database connections (if configured)
- ✅ Initialize all services

## Troubleshooting

### Common Issues

1. **Missing API Keys**
   ```
   Error: GOOGLE_API_KEY is required for Google models
   ```
   Solution: Add your Google API key to the `.env` file

2. **MCP Server Connection Failed**
   ```
   Warning: Could not load tools from pricing server
   ```
   Solution: Ensure MCP services are running and URLs are correct

3. **Configuration Validation Failed**
   ```
   Configuration error: LANGSMITH_API_KEY is required when tracing is enabled
   ```
   Solution: Either add the API key or disable tracing

### Debug Mode

Enable debug mode for detailed logging:

```env
DEBUG=true
LOG_LEVEL=DEBUG
```

This will show:
- Configuration summary on startup
- Detailed error messages
- MCP server connection attempts
- Tool loading progress

## Advanced Configuration

### Custom System Prompts

You can customize the agent's behavior by setting a custom system prompt:

```env
AGENT_SYSTEM_PROMPT=You are an expert sales assistant at Graphisoft specializing in architectural software. Your goal is to understand customer needs and provide tailored solutions.
```

### Multiple MCP Servers

Enable additional MCP servers:

```env
MCP_SEARCH_ENABLED=true
MCP_SEARCH_URL=http://mcp-search:8000/mcp
```

### Runtime Configuration

You can also override configuration at runtime using the `get_runnable_config()` utility:

```python
from agent.config_utils import get_runnable_config

config = get_runnable_config(
    thread_id="user-123",
    additional_config={
        "configurable": {
            "temperature": 0.5,
            "max_steps": 10
        }
    }
)
```

## Security Notes

- Never commit `.env` files to version control
- Use environment-specific files (`.env.development`, `.env.docker`)
- Rotate API keys regularly
- Use secure connections for production MCP servers
- Consider using secrets management for production deployments
