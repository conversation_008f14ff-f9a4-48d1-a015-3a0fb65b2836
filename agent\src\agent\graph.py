from langchain.chat_models import init_chat_model
from langgraph.prebuilt import create_react_agent
from langchain_mcp_adapters.client import MultiServerMCPClient

from contextlib import asynccontextmanager

from agent.util.tools import TOOLS
from agent.util.states import State

@asynccontextmanager
async def make_graph():
    mcp_client = MultiServerMCPClient(
        {
            "pricing": {
                "url": "http://mcp-pricing:8000/mcp",
                "transport": "streamable_http",
            },
            # TODO: waiting for RAG implementation
            # "search": {
            #     "url": "http://mcp-search:8000/mcp",
            #     "transport": "streamable_http",
            # }
        }
    )

    # Gather all tools for the agent
    tools = []
    tools.extend(TOOLS)

    pricing_tools = await mcp_client.get_tools(server_name="pricing")
    tools.extend(pricing_tools)

    #search_tools = await mcp_client.get_tools(server_name="search")
    #tools.extend(search_tools)

    chat_model = init_chat_model("google_genai:gemini-2.0-flash")

    agent = create_react_agent(
        model = chat_model,
        tools = tools,
        prompt = "You are a helpful sales assistant at Graphisoft and your goal is to assist customers and make an offer.",
        state_schema = State,
    )
    yield agent
